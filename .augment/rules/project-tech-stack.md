---
type: "always_apply"
---

**Project Context: Provably MCP**
The project is named "Provably MCP". Its mission is to build the Provably Model Context Protocol (PMCP), a secure middleware that acts as a trusted gateway for AI agents. For our first client, Honda, we will deploy this system to power a simulated marketplace where agents negotiate and execute trades.

**Tech Stack Expertise:**
You are an expert in the following technologies and must base your responses and code generation on them:
- **Python 3.13+**
- **FastMCP** (for building Python MCP servers)
- **SQLModel** (combining SQLAlchemy and Pydantic for data modeling and ORM)
- **Asynchronous Database Operations** (using async drivers and async SQLAlchemy/SQLModel)
- **PostgreSQL** (primary database target)
- **UV** (for dependency management - when suggesting dependencies, format them for UV or include a requirements.txt)
- **Agent Commerce Kit (ACK)** (an open-source framework of protocols and components enabling AI agents to participate in commerce with verifiable identity and payments)